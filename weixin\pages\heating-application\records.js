const { heatingApplicationApi } = require('../../api/index.js');

Page({
  data: {
    recordList: [],
    loading: false,
    pendingCount: 0,
    approvedCount: 0
  },

  onLoad() {
    this.loadRecordList();
  },

  onShow() {
    this.loadRecordList();
  },

  /**
   * 加载申请记录列表
   */
  async loadRecordList() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.houseId) {
      wx.showToast({
        title: '请先绑定房屋信息',
        icon: 'none'
      });
      return;
    }

    this.setData({ loading: true });
    wx.showLoading({
      title: '加载记录中...'
    });

    try {
      const res = await heatingApplicationApi.getApplicationList(userInfo.houseId);
      wx.hideLoading();
      this.setData({ loading: false });

      if (res.code === 200) {
        const recordList = res.data || [];
        
        // 统计各状态数量
        let pendingCount = 0;
        let approvedCount = 0;
        
        recordList.forEach(record => {
          // 格式化时间显示
          if (record.applyTime) {
            record.applyTimeFormatted = this.formatDateTime(record.applyTime);
          }
          if (record.approveTime) {
            record.approveTimeFormatted = this.formatDateTime(record.approveTime);
          }
          
          // 设置状态显示文本和样式
          switch (record.currentStatus) {
            case 'pending':
              record.statusText = '待审核';
              record.statusClass = 'pending';
              pendingCount++;
              break;
            case 'approved':
              record.statusText = '已通过';
              record.statusClass = 'approved';
              approvedCount++;
              break;
            case 'rejected':
              record.statusText = '已拒绝';
              record.statusClass = 'rejected';
              break;
            case 'cancelled':
              record.statusText = '已取消';
              record.statusClass = 'cancelled';
              break;
            default:
              record.statusText = '未知状态';
              record.statusClass = 'unknown';
          }
        });

        this.setData({
          recordList,
          pendingCount,
          approvedCount
        });
      } else {
        wx.showToast({
          title: res.message || '加载失败',
          icon: 'none'
        });
      }
    } catch (error) {
      wx.hideLoading();
      this.setData({ loading: false });
      console.error('加载申请记录失败:', error);
      wx.showToast({
        title: error.message || '网络错误，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 格式化日期时间
   */
  formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '';
    
    const date = new Date(dateTimeStr);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  },

  /**
   * 查看申请详情
   */
  viewDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/heating-application/detail?id=${id}`
    });
  },

  /**
   * 取消申请
   */
  async cancelApplication(e) {
    const id = e.currentTarget.dataset.id;
    const record = this.data.recordList.find(item => item.id === id);
    
    if (!record) {
      wx.showToast({
        title: '申请记录不存在',
        icon: 'none'
      });
      return;
    }

    if (record.currentStatus !== 'pending') {
      wx.showToast({
        title: '只能取消待审核的申请',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认取消',
      content: '确定要取消这个申请吗？取消后不可恢复。',
      success: async (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '取消中...'
          });

          try {
            const result = await heatingApplicationApi.cancelApplication(id);
            wx.hideLoading();

            if (result.code === 200) {
              wx.showToast({
                title: '取消成功',
                icon: 'success'
              });
              // 重新加载列表
              this.loadRecordList();
            } else {
              wx.showToast({
                title: result.message || '取消失败',
                icon: 'none'
              });
            }
          } catch (error) {
            wx.hideLoading();
            console.error('取消申请失败:', error);
            wx.showToast({
              title: error.message || '网络错误，请重试',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  /**
   * 跳转到申请页面
   */
  goToApply() {
    wx.navigateTo({
      url: '/pages/heating-application/index'
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadRecordList().then(() => {
      wx.stopPullDownRefresh();
    });
  }
});
