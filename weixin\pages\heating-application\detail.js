const { heatingApplicationApi } = require('../../api/index.js');

Page({
  data: {
    applicationDetail: null,
    loading: true
  },

  onLoad(options) {
    const id = options.id;
    if (id) {
      this.loadApplicationDetail(id);
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
    }
  },

  /**
   * 加载申请详情
   */
  async loadApplicationDetail(id) {
    wx.showLoading({
      title: '加载中...'
    });

    try {
      const res = await heatingApplicationApi.getApplicationDetail(id);
      wx.hideLoading();

      if (res.code === 200) {
        const detail = res.data;
        
        // 格式化时间显示
        if (detail.applyTime) {
          detail.applyTimeFormatted = this.formatDateTime(detail.applyTime);
        }
        if (detail.approveTime) {
          detail.approveTimeFormatted = this.formatDateTime(detail.approveTime);
        }
        
        // 设置状态显示文本和样式
        switch (detail.currentStatus) {
          case 'pending':
            detail.statusText = '待审核';
            detail.statusClass = 'pending';
            break;
          case 'approved':
            detail.statusText = '已通过';
            detail.statusClass = 'approved';
            break;
          case 'rejected':
            detail.statusText = '已拒绝';
            detail.statusClass = 'rejected';
            break;
          case 'cancelled':
            detail.statusText = '已取消';
            detail.statusClass = 'cancelled';
            break;
          default:
            detail.statusText = '未知状态';
            detail.statusClass = 'unknown';
        }

        this.setData({
          applicationDetail: detail,
          loading: false
        });
      } else {
        wx.showToast({
          title: res.message || '加载失败',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 2000);
      }
    } catch (error) {
      wx.hideLoading();
      console.error('加载申请详情失败:', error);
      wx.showToast({
        title: error.message || '网络错误，请重试',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
    }
  },

  /**
   * 格式化日期时间
   */
  formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '';
    
    const date = new Date(dateTimeStr);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  },

  /**
   * 取消申请
   */
  async cancelApplication() {
    const detail = this.data.applicationDetail;
    
    if (!detail || detail.currentStatus !== 'pending') {
      wx.showToast({
        title: '只能取消待审核的申请',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认取消',
      content: '确定要取消这个申请吗？取消后不可恢复。',
      success: async (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '取消中...'
          });

          try {
            const result = await heatingApplicationApi.cancelApplication(detail.id);
            wx.hideLoading();

            if (result.code === 200) {
              wx.showToast({
                title: '取消成功',
                icon: 'success'
              });
              
              // 更新状态
              this.setData({
                'applicationDetail.currentStatus': 'cancelled',
                'applicationDetail.statusText': '已取消',
                'applicationDetail.statusClass': 'cancelled'
              });
            } else {
              wx.showToast({
                title: result.message || '取消失败',
                icon: 'none'
              });
            }
          } catch (error) {
            wx.hideLoading();
            console.error('取消申请失败:', error);
            wx.showToast({
              title: error.message || '网络错误，请重试',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  /**
   * 复制文本到剪贴板
   */
  copyText(e) {
    const text = e.currentTarget.dataset.text;
    if (text) {
      wx.setClipboardData({
        data: text,
        success: () => {
          wx.showToast({
            title: '已复制',
            icon: 'success'
          });
        }
      });
    }
  }
});
