package com.heating.service.impl;

import com.heating.dto.bill.*;
import com.heating.entity.bill.TBill;
import com.heating.entity.bill.TPayment;
import com.heating.entity.bill.TOverdueRecord;
import com.heating.entity.bill.TStopSupplyApply;
import com.heating.entity.bill.THeatingFeeRule;
import com.heating.entity.House;
import com.heating.repository.TBillRepository;
import com.heating.repository.TPaymentRepository;
import com.heating.repository.TOverdueRecordRepository;
import com.heating.repository.StopSupplyApplyRepository;
import com.heating.repository.THeatingFeeRuleRepository;
import com.heating.repository.HouseRepository;
import com.heating.service.BillService;
import com.heating.service.HeatUnitService;
import com.heating.service.HeatingFeeRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BillServiceImpl implements BillService {

    @Autowired
    private TBillRepository billRepository;

    @Autowired
    private TPaymentRepository paymentRepository;

    @Autowired
    private HeatUnitService heatUnitService;
    @Autowired
    private TOverdueRecordRepository overdueRecordRepository;

    @Autowired
    private HouseRepository houseRepository;

    @Autowired
    private StopSupplyApplyRepository stopSupplyApplyRepository;

    @Autowired
    private THeatingFeeRuleRepository heatingFeeRuleRepository;

    @Autowired
    private HeatingFeeRuleService heatingFeeRuleService;

    @Override
    public BillListResponse getBillList(BillListRequest request) {
        try {
            log.info("获取账单列表请求参数: {}", request);
            
            // 验证房屋ID是否存在
            if (request.getHouseId() == null) {
                throw new RuntimeException("房屋ID不能为空");
            }
            
            // 检查房屋是否存在
            Optional<House> houseOpt = houseRepository.findById(request.getHouseId());
            if (!houseOpt.isPresent()) {
                log.warn("房屋不存在: houseId={}", request.getHouseId());
                throw new RuntimeException("房屋信息不存在");
            }
            House house = houseOpt.get();
            log.info("找到房屋信息: {}", house.getHouseNumber());
            
            // 先检查该房屋下是否有账单
            long billCount = billRepository.countByHouseId(request.getHouseId());
            log.info("房屋ID={}下共有{}条账单", request.getHouseId(), billCount);
            
            Pageable pageable = PageRequest.of(request.getPage() - 1, request.getPageSize());
            Page<TBill> billPage;
            //billPage = billRepository.findByHouseIdOrderByHeatYearDesc(request.getHouseId(), pageable);
            if("unpaid".equals(request.getStatus())) {
                log.info("查询未支付账单");
                billPage = billRepository.findByHouseIdAndStatus(request.getHouseId(), TBill.BillStatus.unpaid, pageable);
            } else if ("paid".equals(request.getStatus())) {
                log.info("查询已支付账单");
                billPage = billRepository.findByHouseIdAndStatus(request.getHouseId(), TBill.BillStatus.paid, pageable);
            } else if ("partial_paid".equals(request.getStatus())) {
                log.info("查询部分支付账单");
                billPage = billRepository.findByHouseIdAndStatus(request.getHouseId(), TBill.BillStatus.partial_paid, pageable);
            } else {
                log.info("查询所有账单，房屋ID: {}", request.getHouseId());
                billPage = billRepository.findByHouseIdOrderByHeatYearDesc(request.getHouseId(), pageable);
            }
            
            log.info("查询结果: 总数={}, 当前页数据={}", billPage.getTotalElements(), billPage.getContent().size());
            
            BillListResponse response = new BillListResponse();
            response.setTotal((int) billPage.getTotalElements());
            response.setPage(request.getPage());
            response.setPageSize(request.getPageSize());
            
            // 构建账单详情列表
            List<BillListResponse.BillDetail> billDetails = billPage.getContent().stream()
                    .map(bill -> {
                        log.debug("处理账单: ID={}, 年份={}, 状态={}", bill.getId(), bill.getHeatYear(), bill.getStatus());
                        return convertToBillDetail(bill, house);
                    })
                    .collect(Collectors.toList());
            response.setBills(billDetails);
            
            // 构建汇总信息
            BillListResponse.BillSummary summary = buildBillSummary(request.getHouseId());
            response.setSummary(summary);
            
            log.info("返回账单列表: 总数={}, 当前页={}", response.getTotal(), response.getBills().size());
            return response;
        } catch (Exception e) {
            log.error("获取账单列表失败", e);
            throw new RuntimeException("获取账单列表失败: " + e.getMessage());
        }
    }

    @Override
    public BillListResponse.BillDetail getBillDetail(BillDetailRequest request) {
        try {
            log.info("获取账单详情: {}", request);

            Optional<TBill> billOpt = billRepository.findById(request.getBillId());
            if (!billOpt.isPresent()) {
                throw new RuntimeException("账单不存在");
            }

            TBill bill = billOpt.get();
            Optional<House> houseOpt = houseRepository.findById(bill.getHouseId());
            House house = houseOpt.orElse(null);

            return convertToBillDetail(bill, house);

        } catch (Exception e) {
            log.error("获取账单详情失败", e);
            throw new RuntimeException("获取账单详情失败: " + e.getMessage());
        }
    }

    @Override
    public BillDetailViewResponse viewBillDetail(BillDetailViewRequest request) {
        try {
            log.info("查看账单详情开始: {}", request);

            // 第一步：前端触发与用户验证
            Long houseId = request.getHouseId();
            if (houseId == null) {
                throw new RuntimeException("房屋ID不能为空");
            }

            // 验证房屋信息是否存在
            Optional<House> houseOpt = houseRepository.findById(houseId);
            if (!houseOpt.isPresent()) {
                throw new RuntimeException("房屋信息不存在");
            }
            House house = houseOpt.get();

            // 获取供暖年度（如果未指定则使用当前年度）
            Integer heatingYear = request.getHeatingYear();
            if (heatingYear == null) {
                heatingYear = getCurrentHeatingYear();
            }
            log.info("查询房屋ID: {}, 供暖年度: {}", houseId, heatingYear);

            // 第二步：查询核心账单数据
            // 1. 查询本年度账单
            Optional<TBill> billOpt = billRepository.findByHouseIdAndHeatYear(houseId, heatingYear);
            log.info("账单查询结果: {}", billOpt.isPresent() ? "找到账单" : "未找到账单");

            // 2. 查询停供申请（查询该用户在当前供暖年度是否有状态为approved的停供申请）
            Optional<TStopSupplyApply> stopSupplyApplyOpt = stopSupplyApplyRepository
                    .findByHouseIdAndHeatingYearAndStatus(houseId, heatingYear, TStopSupplyApply.ApplyStatus.approved);
            log.info("停供申请查询结果: {}", stopSupplyApplyOpt.isPresent() ? "找到已批准的停供申请" : "无停供申请");

            // 3. 查询供暖费用规则（用于计算停供后的结算金额）
            Optional<THeatingFeeRule> heatingRuleOpt = heatingFeeRuleRepository.findCurrentActiveRule();
            if (!heatingRuleOpt.isPresent()) {
                log.warn("未找到有效的供暖费用规则");
            }

            // 构建响应对象
            BillDetailViewResponse response = new BillDetailViewResponse();

            // 设置基本信息（无论是否有账单都要显示）
            response.setBasicInfo(buildBasicInfo(house, heatingYear));

            if (!billOpt.isPresent()) {
                // 场景：无当前账单 - 系统尚未为本年度生成账单
                log.info("场景：无当前账单");
                response.setBillInfo(buildEmptyBillInfo());
                response.setPaymentRecords(new ArrayList<>());
                response.setOverdueInfo(buildEmptyOverdueInfo());
                return response;
            }

            // 有账单数据，进入第三步：判断用户状态并生成最终账单视图
            TBill bill = billOpt.get();
            log.info("账单基本信息: ID={}, 总金额={}, 已缴金额={}, 状态={}",
                    bill.getId(), bill.getTotalAmount(), bill.getPaidAmount(), bill.getStatus());

            // 4. 查询缴费记录
            List<BillDetailViewResponse.PaymentRecord> paymentRecords = buildPaymentRecords(bill.getId());

            // 5. 查询欠费记录（逾期信息）
            BillDetailViewResponse.OverdueInfo overdueInfo = buildOverdueInfo(bill.getId());

            if (!stopSupplyApplyOpt.isPresent()) {
                // 场景1：正常供暖（无停供申请或申请未获批）
                log.info("场景1：正常供暖");
                response.setBillInfo(buildNormalBillInfo(bill, overdueInfo));
                response.setPaymentRecords(paymentRecords);
                response.setOverdueInfo(overdueInfo);
            } else {
                // 有已批准的停供申请，需要根据停供时间和缴费情况判断具体场景
                TStopSupplyApply stopSupplyApply = stopSupplyApplyOpt.get();
                THeatingFeeRule heatingRule = heatingRuleOpt.orElse(null);

                log.info("停供申请详情: 停供开始日期={}, 申请状态={}",
                        stopSupplyApply.getStopStartDate(), stopSupplyApply.getStatus());

                if (heatingRule != null) {
                    // 判断是供暖开始前还是开始后申请停供
                    boolean isBeforeHeatingStart = !stopSupplyApply.getStopStartDate().isAfter(heatingRule.getHeatingStartDate());

                    if (isBeforeHeatingStart) {
                        // 场景2：在供暖开始前申请停供并获批
                        log.info("场景2：在供暖开始前申请停供并获批");
                        response.setBillInfo(buildPreHeatingStopBillInfo(bill, stopSupplyApply));
                    } else {
                        // 判断是场景3还是场景4
                        boolean isFullyPaid = bill.getPaidAmount().compareTo(bill.getTotalAmount()) >= 0;

                        if (isFullyPaid) {
                            // 场景4：在供暖开始后申请停供并获批（此前已全额缴费）
                            log.info("场景4：在供暖开始后申请停供并获批（此前已全额缴费）");
                            response.setBillInfo(buildPostHeatingStopFullPaidBillInfo(bill, stopSupplyApply, heatingRule));
                        } else {
                            // 场景3：在供暖开始后申请停供并获批（此前未缴费）
                            log.info("场景3：在供暖开始后申请停供并获批（此前未缴费）");
                            response.setBillInfo(buildPostHeatingStopUnpaidBillInfo(bill, stopSupplyApply, heatingRule));
                        }
                    }
                } else {
                    // 没有供暖规则，按正常流程处理
                    log.warn("未找到供暖规则，按正常流程处理");
                    response.setBillInfo(buildNormalBillInfo(bill, overdueInfo));
                }

                response.setPaymentRecords(paymentRecords);
                response.setOverdueInfo(overdueInfo);
            }

            log.info("查看账单详情成功: billId={}, houseId={}, heatingYear={}",
                    bill.getId(), houseId, heatingYear);
            return response;

        } catch (Exception e) {
            log.error("查看账单详情失败", e);
            throw new RuntimeException("查看账单详情失败: " + e.getMessage());
        }
    }
    @Override
    public PaymentListResponse getPaymentListByBillId(Long billId) {
        try {
            log.info("根据账单ID获取缴费记录: billId={}", billId);
            
            // 验证账单是否存在
            Optional<TBill> billOpt = billRepository.findById(billId);
            if (!billOpt.isPresent()) {
                throw new RuntimeException("账单不存在");
            }
            
            // 根据账单ID查询缴费记录
            List<TPayment> payments = paymentRepository.findByBillIdOrderByPaymentDateDesc(billId);
            
            PaymentListResponse response = new PaymentListResponse();
            response.setTotal(payments.size());
            response.setPage(1);
            response.setPageSize(payments.size());
            
            // 构建缴费记录详情列表
            List<PaymentListResponse.PaymentDetail> paymentDetails = payments.stream()
                    .map(this::convertToPaymentDetail)
                    .collect(Collectors.toList());
            response.setPayments(paymentDetails);
            
            // 构建汇总信息
            PaymentListResponse.PaymentSummary summary = buildPaymentSummary(payments);
            response.setSummary(summary);
            
            return response;
            
        } catch (Exception e) {
            log.error("根据账单ID获取缴费记录失败", e);
            throw new RuntimeException("获取缴费记录失败: " + e.getMessage());
        }
    }

    private BillListResponse.BillDetail convertToBillDetail(TBill bill, House house) {
        BillListResponse.BillDetail detail = new BillListResponse.BillDetail();
        detail.setId(bill.getId());
        detail.setPeriod(bill.getHeatYear() + "-" + (bill.getHeatYear() + 1) + " 供暖季");
        detail.setHouseNumber(house != null ? house.getHouseNumber() : "");
        detail.setAddress(house != null ? house.getRoomNo() : "");
        detail.setArea(house != null && house.getArea() != null ? house.getArea().toString() : "");
        detail.setHouseName(house.getHouseMaster());

        // 根据计费规则ID获取实际单价
        BigDecimal unitPrice = heatingFeeRuleService.getUnitPriceByRuleId(bill.getHeatFeeRuleId().longValue());
        detail.setUnitPrice(unitPrice.toString());
        
        detail.setAmount(bill.getTotalAmount().toString());
        detail.setPaidAmount(bill.getPaidAmount().toString());
        detail.setStatus(bill.getStatus().name());
        detail.setStatusIcon(getStatusIcon(bill.getStatus()));
        detail.setStatusText(getStatusText(bill.getStatus()));
        detail.setCreateDate(bill.getCreatedAt() != null ? bill.getCreatedAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) : "");
        detail.setDueDate(bill.getDueDate() != null ? bill.getDueDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) : "");
        detail.setLastPaidDate(bill.getLastPaidDate() != null ? bill.getLastPaidDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) : "");
        detail.setRemark(bill.getRemark());
        detail.setHeatYear(bill.getHeatYear());
        detail.setIsHeating(bill.getIsHeating());
        return detail;
    }

    private PaymentListResponse.PaymentDetail convertToPaymentDetail(TPayment payment) {
        PaymentListResponse.PaymentDetail detail = new PaymentListResponse.PaymentDetail();
        detail.setId(payment.getId());
        detail.setBillId(payment.getBillId());
        detail.setRoomNo(payment.getRoomNo());
        detail.setHeatYear(payment.getHeatYear().toString());
        detail.setPaymentMethod(payment.getPaymentMethod().name());
        detail.setPaymentMethodText(getPaymentMethodText(payment.getPaymentMethod()));
        detail.setAmount(payment.getAmount().toString());
        detail.setTransactionNo(payment.getTransactionNo());
        detail.setPaymentDate(payment.getPaymentDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        detail.setRemark(payment.getRemark());
        detail.setPeriod(payment.getHeatYear() + "-" + (payment.getHeatYear() + 1) + " 供暖季");
        return detail;
    }

    private BillListResponse.BillSummary buildBillSummary(Long houseId) {
        BillListResponse.BillSummary summary = new BillListResponse.BillSummary();
        
        List<TBill> allBills = billRepository.findByHouseIdOrderByHeatYearDesc(houseId);
        
        BigDecimal totalUnpaid = BigDecimal.ZERO;
        BigDecimal totalPaid = BigDecimal.ZERO;
        int unpaidCount = 0;
        int paidCount = 0;
        
        for (TBill bill : allBills) {
            if (bill.getStatus() == TBill.BillStatus.unpaid || bill.getStatus() == TBill.BillStatus.overdue) {
                totalUnpaid = totalUnpaid.add(bill.getTotalAmount().subtract(bill.getPaidAmount()));
                unpaidCount++;
            } else if (bill.getStatus() == TBill.BillStatus.paid) {
                totalPaid = totalPaid.add(bill.getPaidAmount());
                paidCount++;
            }
        }
        
        summary.setTotalUnpaid(totalUnpaid);
        summary.setTotalPaid(totalPaid);
        summary.setUnpaidCount(unpaidCount);
        summary.setPaidCount(paidCount);
        
        return summary;
    }

    private PaymentListResponse.PaymentSummary buildPaymentSummary(List<TPayment> payments) {
        PaymentListResponse.PaymentSummary summary = new PaymentListResponse.PaymentSummary();
        
        BigDecimal totalAmount = payments.stream()
                .map(TPayment::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        summary.setTotalAmount(totalAmount);
        summary.setTotalCount(payments.size());
        
        return summary;
    }

    private String getStatusIcon(TBill.BillStatus status) {
        switch (status) {
            case paid: return "✅";
            case unpaid: return "🔴";
            case partial_paid: return "🟡";
            case overdue: return "🔴";
            default: return "🔴";
        }
    }

    private String getStatusText(TBill.BillStatus status) {
        switch (status) {
            case paid: return "已缴费";
            case unpaid: return "待缴费";
            case partial_paid: return "部分缴费";
            case overdue: return "逾期";
            default: return "待缴费";
        }
    }

    private String getPaymentMethodText(TPayment.PaymentMethod method) {
        switch (method) {
            case wechat: return "微信支付";
            case alipay: return "支付宝";
            case bank_transfer: return "银行转账";
            case cash: return "现金";
            default: return "未知";
        }
    }

    @Override
    public PaymentListResponse getPaymentRecordsByHouseId(Long houseId) {
        try {
            log.info("根据房屋ID获取所有缴费记录: houseId={}", houseId);
            
            // 验证房屋是否存在
            Optional<House> houseOpt = houseRepository.findById(houseId);
            if (!houseOpt.isPresent()) {
                throw new RuntimeException("房屋信息不存在");
            }
            
            // 根据房屋ID查询所有缴费记录，按支付时间倒序排列
            List<TPayment> payments = paymentRepository.findByHouseIdOrderByPaymentDateDesc(houseId);
            
            PaymentListResponse response = new PaymentListResponse();
            response.setTotal(payments.size());
            response.setPage(1);
            response.setPageSize(payments.size());
            
            // 构建缴费记录详情列表
            List<PaymentListResponse.PaymentDetail> paymentDetails = payments.stream()
                    .map(this::convertToPaymentDetail)
                    .collect(Collectors.toList());
            response.setPayments(paymentDetails);
            
            // 构建汇总信息
            PaymentListResponse.PaymentSummary summary = buildPaymentSummary(payments);
            response.setSummary(summary);
            
            log.info("查询到{}条缴费记录", payments.size());
            return response;
            
        } catch (Exception e) {
            log.error("根据房屋ID获取缴费记录失败", e);
            throw new RuntimeException("获取缴费记录失败: " + e.getMessage());
        }
    }

    @Override
    public InvoiceDetailResponse getInvoiceDetail(Long paymentId) {
        try {
            log.info("获取票据详情: paymentId={}", paymentId);
            
            // 查询缴费记录
            Optional<TPayment> paymentOpt = paymentRepository.findById(paymentId);
            if (!paymentOpt.isPresent()) {
                throw new RuntimeException("缴费记录不存在");
            }
            
            TPayment payment = paymentOpt.get();
            
            // 查询账单信息
            Optional<TBill> billOpt = billRepository.findById(payment.getBillId());
            if (!billOpt.isPresent()) {
                throw new RuntimeException("账单信息不存在");
            }
            
            TBill bill = billOpt.get();
            
            // 查询房屋信息
            Optional<House> houseOpt = houseRepository.findById(bill.getHouseId());
            House house = houseOpt.orElse(null);
            
            // 构建票据详情
            InvoiceDetailResponse response = new InvoiceDetailResponse();
            response.setInvoiceNo(generateInvoiceNo(payment));
            response.setCreateDate(payment.getPaymentDate().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")));
            response.setPeriod(bill.getHeatYear() + "-11-15-" + (bill.getHeatYear() + 1) + "-03-15");
            response.setOwnerName(house != null ? house.getHouseMaster() : "未知");
            response.setHouseNumber(house != null ? house.getHouseNumber() : "未知房号");
            response.setAddress(house != null ? house.getRoomNo() : "未知地址");
            response.setArea(house != null && house.getArea() != null ? house.getArea().toString() : "0");

            // 根据计费规则ID获取实际单价
            BigDecimal unitPrice = heatingFeeRuleService.getUnitPriceByRuleId(bill.getHeatFeeRuleId().longValue());
            response.setUnitPrice(unitPrice.toString());

            response.setAmount(bill.getTotalAmount().toString());
            response.setPaidAmount(payment.getAmount().toString());
            response.setPaymentMethodText(getPaymentMethodText(payment.getPaymentMethod()));
            response.setPaymentDate(payment.getPaymentDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            response.setTransactionNo(payment.getTransactionNo());
            response.setBillId(bill.getId());
            response.setPaymentId(payment.getId());
            
            return response;
            
        } catch (Exception e) {
            log.error("获取票据详情失败", e);
            throw new RuntimeException("获取票据详情失败: " + e.getMessage());
        }
    }

    @Override
    public String generateInvoicePDF(Long paymentId) {
        try {
            log.info("生成票据PDF: paymentId={}", paymentId);
            
            // 获取票据详情
            InvoiceDetailResponse invoiceDetail = getInvoiceDetail(paymentId);

            // 这里可以集成PDF生成库（如iText、PDFBox等）来生成实际的PDF文件
            // 目前返回模拟的PDF URL
            String pdfFileName = "invoice_" + invoiceDetail.getInvoiceNo() + "_" + System.currentTimeMillis() + ".pdf";
            String pdfUrl = "/uploads/invoices/" + pdfFileName;
            
            // TODO: 实际的PDF生成逻辑
            // 1. 使用PDF模板
            // 2. 填充票据数据
            // 3. 保存到文件系统或云存储
            // 4. 返回访问URL
            
            log.info("PDF生成成功: {}", pdfUrl);
            return pdfUrl;
            
        } catch (Exception e) {
            log.error("生成PDF失败", e);
            throw new RuntimeException("生成PDF失败: " + e.getMessage());
        }
    }

    /**
     * 生成票据编号
     */
    private String generateInvoiceNo(TPayment payment) {
        String dateStr = payment.getPaymentDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return "PJ" + dateStr + String.format("%06d", payment.getId());
    }

    /**
     * 获取支付方式文本
     */
    private String getPaymentMethodText(String paymentMethod) {
        if (paymentMethod == null) {
            return "未知方式";
        }
        
        switch (paymentMethod.toLowerCase()) {
            case "wechat":
            case "weixin":
                return "微信支付";
            case "alipay":
                return "支付宝";
            case "bank":
                return "银行转账";
            case "cash":
                return "现金支付";
            default:
                return paymentMethod;
        }
    }

    /**
     * 获取当前供暖年度
     * @return 当前供暖年度
     */
    private Integer getCurrentHeatingYear() {
        // 供暖年度通常从11月开始到次年3月结束
        // 例如：2024年11月-2025年3月为2024供暖年度
        java.time.LocalDate now = java.time.LocalDate.now();
        int currentYear = now.getYear();
        int currentMonth = now.getMonthValue();

        // 如果是1-10月，则属于上一年的供暖年度
        if (currentMonth <= 10) {
            return currentYear - 1;
        } else {
            // 如果是11-12月，则属于当年的供暖年度
            return currentYear;
        }
    }

    /**
     * 构建基本信息
     * @param house 房屋信息
     * @param heatingYear 供暖年度
     * @return 基本信息
     */
    private BillDetailViewResponse.BasicInfo buildBasicInfo(House house, Integer heatingYear) {
        BillDetailViewResponse.BasicInfo basicInfo = new BillDetailViewResponse.BasicInfo();
        basicInfo.setHouseNumber(house.getHouseNumber());
        basicInfo.setAddress(house.getRoomNo());

        // 转换面积类型从Double到BigDecimal
        Double area = house.getArea();
        basicInfo.setArea(area != null ? BigDecimal.valueOf(area) : BigDecimal.ZERO);
        basicInfo.setHeatingYear(heatingYear + "-" + (heatingYear + 1) + "供暖年度");

        // 设置供暖状态（根据isHeating字段判断）
        String heatingStatus = getHeatingStatusFromHouse(house);
        basicInfo.setHeatingStatus(heatingStatus);
        basicInfo.setHeatingStatusText(getHeatingStatusText(heatingStatus));

        return basicInfo;
    }

    /**
     * 构建账单信息
     * 根据t_bill表数据，计算供热费用、历史欠费金额和应缴费金额
     * @param bill 账单实体
     * @return 账单信息
     */
    private BillDetailViewResponse.BillInfo buildBillInfo(TBill bill) {
        log.info("开始构建账单信息，账单ID: {}", bill.getId());

        BillDetailViewResponse.BillInfo billInfo = new BillDetailViewResponse.BillInfo();
        billInfo.setBillId(bill.getId());

        // 根据计费规则ID获取实际单价
        BigDecimal unitPrice = heatingFeeRuleService.getUnitPriceByRuleId(bill.getHeatFeeRuleId().longValue());
        billInfo.setUnitPrice(unitPrice);
        log.info("获取单价: {} 元/平方米", unitPrice);

        // 1. 供热费用 - 本年度供暖费用（从t_bill表的total_amount获取）
        BigDecimal heatingFee = bill.getTotalAmount();
        billInfo.setHeatingFee(heatingFee);
        log.info("本年度供热费用: {} 元", heatingFee);

        // 2. 历史欠费金额 - 查询该房屋历史年度未缴清的费用
        BigDecimal historicalDebt = calculateHistoricalDebt(bill.getHouseId(), bill.getHeatYear());
        billInfo.setHistoricalDebt(historicalDebt);
        log.info("历史欠费金额: {} 元", historicalDebt);

        // 3. 应缴费金额 - 供热费用 + 历史欠费金额
        BigDecimal totalPayableAmount = heatingFee.add(historicalDebt);
        billInfo.setTotalPayableAmount(totalPayableAmount);
        log.info("应缴费金额: {} 元 (供热费用 {} + 历史欠费 {})", totalPayableAmount, heatingFee, historicalDebt);

        // 4. 实际缴费金额 - 用户实际已缴纳的金额（从t_bill表的paid_amount获取）
        BigDecimal actualPaidAmount = bill.getPaidAmount();
        billInfo.setActualPaidAmount(actualPaidAmount);
        log.info("实际缴费金额: {} 元", actualPaidAmount);

        // 5. 保持原有字段的兼容性
        billInfo.setTotalAmount(bill.getTotalAmount());
        billInfo.setPaidAmount(bill.getPaidAmount());

        // 6. 计算剩余未缴金额 - 应缴费金额 - 实际缴费金额
        BigDecimal remainingAmount = totalPayableAmount.subtract(actualPaidAmount);
        billInfo.setRemainingAmount(remainingAmount);
        log.info("剩余未缴金额: {} 元", remainingAmount);

        // 格式化日期
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        billInfo.setDueDate(bill.getDueDate() != null ? bill.getDueDate().format(dateFormatter) : null);
        billInfo.setLastPaidDate(bill.getLastPaidDate() != null ? bill.getLastPaidDate().format(dateFormatter) : null);

        // 设置状态
        String status = bill.getStatus() != null ? bill.getStatus().name() : "unknown";
        billInfo.setStatus(status);
        billInfo.setStatusText(getBillStatusText(status));
        billInfo.setRemark(bill.getRemark());

        log.info("账单信息构建完成");
        return billInfo;
    }

    /**
     * 构建缴费记录列表
     * @param billId 账单ID
     * @return 缴费记录列表
     */
    private List<BillDetailViewResponse.PaymentRecord> buildPaymentRecords(Long billId) {
        List<TPayment> payments = paymentRepository.findByBillIdOrderByPaymentDateDesc(billId);
        List<BillDetailViewResponse.PaymentRecord> paymentRecords = new ArrayList<>();

        java.time.format.DateTimeFormatter dateTimeFormatter = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        for (TPayment payment : payments) {
            BillDetailViewResponse.PaymentRecord record = new BillDetailViewResponse.PaymentRecord();
            record.setPaymentId(payment.getId());
            record.setAmount(payment.getAmount());

            String paymentMethod = payment.getPaymentMethod() != null ? payment.getPaymentMethod().name() : "unknown";
            record.setPaymentMethod(paymentMethod);
            record.setPaymentMethodText(getPaymentMethodText(paymentMethod));
            record.setPaymentDate(payment.getPaymentDate() != null ?
                    payment.getPaymentDate().format(dateTimeFormatter) : null);
            record.setTransactionNo(payment.getTransactionNo());
            record.setRemark(payment.getRemark());

            paymentRecords.add(record);
        }

        return paymentRecords;
    }

    /**
     * 构建逾期信息
     * @param billId 账单ID
     * @return 逾期信息
     */
    private BillDetailViewResponse.OverdueInfo buildOverdueInfo(Long billId) {
        BillDetailViewResponse.OverdueInfo overdueInfo = new BillDetailViewResponse.OverdueInfo();

        // 查询逾期记录
        Optional<TOverdueRecord> overdueRecordOpt = overdueRecordRepository.findByBillId(billId);

        if (overdueRecordOpt.isPresent() && overdueRecordOpt.get().getStatus() == TOverdueRecord.OverdueStatus.active) {
            TOverdueRecord overdueRecord = overdueRecordOpt.get();

            overdueInfo.setHasOverdue(true);
            overdueInfo.setOverdueDays(overdueRecord.getOverdueDays());
            overdueInfo.setOverdueAmount(overdueRecord.getOverdueAmount());
            overdueInfo.setPenaltyAmount(overdueRecord.getPenaltyAmount());
            overdueInfo.setPenaltyRate(overdueRecord.getPenaltyRate());

            java.time.format.DateTimeFormatter dateFormatter = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd");
            overdueInfo.setFirstOverdueDate(overdueRecord.getFirstOverdueDate() != null ?
                    overdueRecord.getFirstOverdueDate().format(dateFormatter) : null);
            overdueInfo.setLastUpdatedDate(overdueRecord.getLastUpdatedDate() != null ?
                    overdueRecord.getLastUpdatedDate().format(dateFormatter) : null);
        } else {
            overdueInfo.setHasOverdue(false);
            overdueInfo.setOverdueDays(0);
            overdueInfo.setOverdueAmount(BigDecimal.ZERO);
            overdueInfo.setPenaltyAmount(BigDecimal.ZERO);
        }

        return overdueInfo;
    }

    /**
     * 获取供暖状态文本
     * @param status 状态
     * @return 状态文本
     */
    private String getHeatingStatusText(String status) {
        if (status == null) return "未知状态";

        switch (status.toLowerCase()) {
            case "normal":
                return "正常供暖";
            case "stopped":
                return "已停供";
            case "suspended":
                return "暂停供暖";
            default:
                return status;
        }
    }

    /**
     * 获取账单状态文本
     * @param status 状态
     * @return 状态文本
     */
    private String getBillStatusText(String status) {
        if (status == null) return "未知状态";

        switch (status.toLowerCase()) {
            case "unpaid":
                return "未缴费";
            case "partial_paid":
                return "部分缴费";
            case "paid":
                return "已缴清";
            case "overdue":
                return "逾期";
            default:
                return status;
        }
    }

    /**
     * 根据房屋信息获取供暖状态
     * @param house 房屋信息
     * @return 供暖状态
     */
    private String getHeatingStatusFromHouse(House house) {
        if (house.getIsHeating() == null) {
            return "unknown";
        }

        switch (house.getIsHeating()) {
            case 1:
                return "normal";  // 正常供暖
            case 0:
                return "stopped"; // 已停供
            default:
                return "unknown";
        }
    }

    /**
     * 构建空的账单信息（当没有账单数据时）
     * @return 空的账单信息
     */
    private BillDetailViewResponse.BillInfo buildEmptyBillInfo() {
        BillDetailViewResponse.BillInfo billInfo = new BillDetailViewResponse.BillInfo();
        billInfo.setBillId(null);
        billInfo.setUnitPrice(BigDecimal.ZERO);
        billInfo.setTotalAmount(BigDecimal.ZERO);
        billInfo.setPaidAmount(BigDecimal.ZERO);
        billInfo.setRemainingAmount(BigDecimal.ZERO);
        billInfo.setDueDate(null);
        billInfo.setLastPaidDate(null);
        billInfo.setStatus("no_bill");
        billInfo.setStatusText("暂无账单");
        billInfo.setRemark("暂无此供暖季的账单信息");
        return billInfo;
    }

    /**
     * 构建空的逾期信息（当没有逾期记录时）
     * @return 空的逾期信息
     */
    private BillDetailViewResponse.OverdueInfo buildEmptyOverdueInfo() {
        BillDetailViewResponse.OverdueInfo overdueInfo = new BillDetailViewResponse.OverdueInfo();
        overdueInfo.setHasOverdue(false);
        overdueInfo.setOverdueDays(0);
        overdueInfo.setOverdueAmount(BigDecimal.ZERO);
        overdueInfo.setPenaltyAmount(BigDecimal.ZERO);
        overdueInfo.setPenaltyRate(BigDecimal.ZERO);
        overdueInfo.setFirstOverdueDate(null);
        overdueInfo.setLastUpdatedDate(null);
        return overdueInfo;
    }

    /**
     * 场景1：构建正常供暖的账单信息
     * 按标准账单流程处理，包含滞纳金计算和历史欠费
     * @param bill 账单实体
     * @param overdueInfo 逾期信息
     * @return 账单信息
     */
    private BillDetailViewResponse.BillInfo buildNormalBillInfo(TBill bill, BillDetailViewResponse.OverdueInfo overdueInfo) {
        log.info("构建正常供暖账单信息");

        // 使用统一的buildBillInfo方法构建基础信息
        BillDetailViewResponse.BillInfo billInfo = buildBillInfo(bill);

        // 设置状态
        String status = bill.getStatus() != null ? bill.getStatus().name() : "unknown";
        billInfo.setStatus(status);

        // 根据逾期情况设置状态文本和备注
        if (overdueInfo.getHasOverdue() && overdueInfo.getOverdueDays() > 0) {
            billInfo.setStatusText("已逾期" + overdueInfo.getOverdueDays() + "天");

            // 计算包含滞纳金的总需支付金额
            BigDecimal totalNeedPay = billInfo.getRemainingAmount().add(overdueInfo.getPenaltyAmount());

            String remarkWithPenalty = String.format(
                "⚠️ 滞纳金：￥%.2f，当前需支付：￥%.2f\n" +
                "详细说明：\n" +
                "- 供热费用：￥%.2f\n" +
                "- 历史欠费：￥%.2f\n" +
                "- 滞纳金：￥%.2f\n" +
                "- 总计需支付：￥%.2f",
                overdueInfo.getPenaltyAmount(),
                totalNeedPay,
                billInfo.getHeatingFee(),
                billInfo.getHistoricalDebt(),
                overdueInfo.getPenaltyAmount(),
                totalNeedPay
            );
            billInfo.setRemark(remarkWithPenalty);
        } else {
            billInfo.setStatusText(getBillStatusText(status));

            // 设置详细的费用说明
            String detailRemark = String.format(
                "费用详情：\n" +
                "- 供热费用：￥%.2f\n" +
                "- 历史欠费：￥%.2f\n" +
                "- 应缴费金额：￥%.2f\n" +
                "- 实际缴费金额：￥%.2f\n" +
                "- 剩余未缴：￥%.2f",
                billInfo.getHeatingFee(),
                billInfo.getHistoricalDebt(),
                billInfo.getTotalPayableAmount(),
                billInfo.getActualPaidAmount(),
                billInfo.getRemainingAmount()
            );

            // 如果原有备注不为空，则追加到详细说明后面
            if (bill.getRemark() != null && !bill.getRemark().trim().isEmpty()) {
                detailRemark += "\n\n备注：" + bill.getRemark();
            }

            billInfo.setRemark(detailRemark);
        }

        return billInfo;
    }

    /**
     * 场景2：构建供暖开始前申请停供并获批的账单信息
     * 账单金额在生成时已按min_payment_rate折扣，包含历史欠费计算
     * @param bill 账单实体
     * @param stopSupplyApply 停供申请
     * @return 账单信息
     */
    private BillDetailViewResponse.BillInfo buildPreHeatingStopBillInfo(TBill bill, TStopSupplyApply stopSupplyApply) {
        log.info("构建供暖开始前停供账单信息");

        // 使用统一的buildBillInfo方法构建基础信息
        BillDetailViewResponse.BillInfo billInfo = buildBillInfo(bill);

        // 设置状态
        String status = bill.getStatus() != null ? bill.getStatus().name() : "unknown";
        billInfo.setStatus(status);
        billInfo.setStatusText(getBillStatusText(status));

        // 格式化日期
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 设置停供相关备注，包含详细的费用说明
        String stopSupplyRemark = String.format(
            "✅ 您已申请本年度停供，按30%%收取基础热损费。\n" +
            "停供生效日期：%s\n\n" +
            "费用详情：\n" +
            "- 供热费用（30%%折扣）：￥%.2f\n" +
            "- 历史欠费：￥%.2f\n" +
            "- 应缴费金额：￥%.2f\n" +
            "- 实际缴费金额：￥%.2f\n" +
            "- 剩余未缴：￥%.2f",
            stopSupplyApply.getStopStartDate().format(dateFormatter),
            billInfo.getHeatingFee(),
            billInfo.getHistoricalDebt(),
            billInfo.getTotalPayableAmount(),
            billInfo.getActualPaidAmount(),
            billInfo.getRemainingAmount()
        );

        billInfo.setRemark(stopSupplyRemark);

        return billInfo;
    }

    /**
     * 场景3：构建供暖开始后申请停供并获批的账单信息（此前未缴费）
     * 原账单欠费记录已被核销，用户需缴纳"结算款"，包含历史欠费计算
     * @param bill 账单实体
     * @param stopSupplyApply 停供申请
     * @param heatingRule 供暖规则
     * @return 账单信息
     */
    private BillDetailViewResponse.BillInfo buildPostHeatingStopUnpaidBillInfo(TBill bill, TStopSupplyApply stopSupplyApply, THeatingFeeRule heatingRule) {
        log.info("构建供暖开始后停供账单信息（此前未缴费）");

        // 使用统一的buildBillInfo方法构建基础信息
        BillDetailViewResponse.BillInfo billInfo = buildBillInfo(bill);

        // 设置状态为已调整
        billInfo.setStatus("adjusted");
        billInfo.setStatusText("已调整");

        // 计算实际应缴的结算金额
        BigDecimal settlementAmount = calculateSettlementAmount(bill, stopSupplyApply, heatingRule);

        // 计算实际供暖天数
        long actualHeatingDays = calculateActualHeatingDays(stopSupplyApply.getStopStartDate(), heatingRule.getHeatingStartDate());

        // 格式化日期
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 设置详细的停供说明，包含费用详情
        String remark = String.format(
            "✅ 您的停供申请已生效！\n" +
            "说明：您只需缴纳 **￥%.2f**（%d天供暖费）作为结算款，原滞纳金已免除。\n" +
            "停供生效日期：%s\n\n" +
            "费用详情：\n" +
            "- 供热费用：￥%.2f\n" +
            "- 历史欠费：￥%.2f\n" +
            "- 应缴费金额：￥%.2f\n" +
            "- 实际缴费金额：￥%.2f\n" +
            "- 剩余未缴：￥%.2f\n" +
            "- 结算金额：￥%.2f",
            settlementAmount,
            actualHeatingDays,
            stopSupplyApply.getStopStartDate().format(dateFormatter),
            billInfo.getHeatingFee(),
            billInfo.getHistoricalDebt(),
            billInfo.getTotalPayableAmount(),
            billInfo.getActualPaidAmount(),
            billInfo.getRemainingAmount(),
            settlementAmount
        );
        billInfo.setRemark(remark);

        return billInfo;
    }

    /**
     * 场景4：构建供暖开始后申请停供并获批的账单信息（此前已全额缴费）
     * 用户多缴，已发起退费，包含历史欠费计算
     * @param bill 账单实体
     * @param stopSupplyApply 停供申请
     * @param heatingRule 供暖规则
     * @return 账单信息
     */
    private BillDetailViewResponse.BillInfo buildPostHeatingStopFullPaidBillInfo(TBill bill, TStopSupplyApply stopSupplyApply, THeatingFeeRule heatingRule) {
        log.info("构建供暖开始后停供账单信息（此前已全额缴费）");

        // 使用统一的buildBillInfo方法构建基础信息
        BillDetailViewResponse.BillInfo billInfo = buildBillInfo(bill);

        // 设置状态为已缴清
        billInfo.setStatus("paid");
        billInfo.setStatusText("已缴清");
        billInfo.setRemainingAmount(BigDecimal.ZERO); // 剩余金额为0

        // 计算实际应缴的结算金额
        BigDecimal settlementAmount = calculateSettlementAmount(bill, stopSupplyApply, heatingRule);

        // 计算应退金额（基于实际缴费金额）
        BigDecimal refundAmount = billInfo.getActualPaidAmount().subtract(settlementAmount);

        // 格式化日期
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 设置详细的退费说明，包含费用详情
        String remark = String.format(
            "✅ 您的停供申请已生效！\n" +
            "说明：因您已多缴费用，系统已为您发起退费申请，金额 **￥%.2f**。\n" +
            "状态：退费申请已提交，财务将在7个工作日内处理。\n" +
            "停供生效日期：%s\n\n" +
            "费用详情：\n" +
            "- 供热费用：￥%.2f\n" +
            "- 历史欠费：￥%.2f\n" +
            "- 应缴费金额：￥%.2f\n" +
            "- 实际缴费金额：￥%.2f\n" +
            "- 结算金额：￥%.2f\n" +
            "- 退费金额：￥%.2f",
            refundAmount,
            stopSupplyApply.getStopStartDate().format(dateFormatter),
            billInfo.getHeatingFee(),
            billInfo.getHistoricalDebt(),
            billInfo.getTotalPayableAmount(),
            billInfo.getActualPaidAmount(),
            settlementAmount,
            refundAmount
        );
        billInfo.setRemark(remark);

        return billInfo;
    }

    /**
     * 计算停供后的结算金额
     * 根据业务逻辑文档：取"按天折算费用"和"最低基础费"中的较高者
     * @param bill 账单实体
     * @param stopSupplyApply 停供申请
     * @param heatingRule 供暖规则
     * @return 结算金额
     */
    private BigDecimal calculateSettlementAmount(TBill bill, TStopSupplyApply stopSupplyApply, THeatingFeeRule heatingRule) {
        log.info("开始计算停供结算金额");

        // 1. 获取基础数据
        BigDecimal totalAmount = bill.getTotalAmount(); // 总金额，如2500.00元
        LocalDate heatingStartDate = heatingRule.getHeatingStartDate(); // 供暖开始日期，如2024-11-15
        LocalDate heatingEndDate = heatingRule.getHeatingEndDate(); // 供暖结束日期，如2025-03-15
        LocalDate stopStartDate = stopSupplyApply.getStopStartDate(); // 停供生效日期，如2024-12-01
        BigDecimal minPaymentRate = heatingRule.getMinPaymentRate(); // 最低缴费比例，如0.3（30%）

        log.info("基础数据 - 总金额: {}, 供暖开始: {}, 供暖结束: {}, 停供开始: {}, 最低比例: {}",
                totalAmount, heatingStartDate, heatingEndDate, stopStartDate, minPaymentRate);

        // 2. 计算总供暖天数
        long totalHeatingDays = calculateTotalHeatingDays(heatingStartDate, heatingEndDate);
        log.info("总供暖天数: {}", totalHeatingDays);

        // 3. 计算实际供暖天数（从供暖开始到停供生效）
        long actualHeatingDays = calculateActualHeatingDays(stopStartDate, heatingStartDate);
        log.info("实际供暖天数: {}", actualHeatingDays);

        // 4. 计算按天折算的费用
        BigDecimal dailyFee = totalAmount.divide(BigDecimal.valueOf(totalHeatingDays), 2, RoundingMode.HALF_UP);
        BigDecimal dailyCalculatedFee = dailyFee.multiply(BigDecimal.valueOf(actualHeatingDays));
        log.info("每天费用: {}, 按天折算费用: {}", dailyFee, dailyCalculatedFee);

        // 5. 计算最低基础热损费
        BigDecimal minBasicFee = totalAmount.multiply(minPaymentRate);
        log.info("最低基础费: {}", minBasicFee);

        // 6. 确定最终结算金额：取较高者
        BigDecimal settlementAmount = dailyCalculatedFee.max(minBasicFee);
        log.info("最终结算金额: {} (取 {} 和 {} 中的较高者)", settlementAmount, dailyCalculatedFee, minBasicFee);

        return settlementAmount;
    }

    /**
     * 计算总供暖天数
     * @param startDate 供暖开始日期
     * @param endDate 供暖结束日期
     * @return 总天数
     */
    private long calculateTotalHeatingDays(LocalDate startDate, LocalDate endDate) {
        return java.time.temporal.ChronoUnit.DAYS.between(startDate, endDate) + 1; // 包含结束日
    }

    /**
     * 计算实际供暖天数
     * @param stopDate 停供日期
     * @param heatingStartDate 供暖开始日期
     * @return 实际供暖天数
     */
    private long calculateActualHeatingDays(LocalDate stopDate, LocalDate heatingStartDate) {
        // 如果停供日期在供暖开始之前或当天，则实际供暖天数为0
        if (!stopDate.isAfter(heatingStartDate)) {
            return 0;
        }
        // 计算从供暖开始到停供开始的天数（不包含停供当天）
        return java.time.temporal.ChronoUnit.DAYS.between(heatingStartDate, stopDate);
    }

    /**
     * 计算历史欠费金额
     * 查询该房屋在历史年度（当前年度之前）未缴清的费用总和
     * @param houseId 房屋ID
     * @param currentHeatingYear 当前供暖年度
     * @return 历史欠费金额
     */
    private BigDecimal calculateHistoricalDebt(Long houseId, Integer currentHeatingYear) {
        log.info("开始计算历史欠费金额，房屋ID: {}, 当前供暖年度: {}", houseId, currentHeatingYear);

        try {
            // 查询该房屋在当前年度之前的所有账单
            List<TBill> historicalBills = billRepository.findByHouseIdAndHeatYearLessThanOrderByHeatYearDesc(
                houseId, currentHeatingYear);

            log.info("找到历史账单数量: {}", historicalBills.size());

            BigDecimal totalHistoricalDebt = BigDecimal.ZERO;

            for (TBill historicalBill : historicalBills) {
                // 计算每个历史账单的欠费金额 = 应缴金额 - 已缴金额
                BigDecimal billDebt = historicalBill.getTotalAmount().subtract(historicalBill.getPaidAmount());

                // 只统计欠费金额大于0的账单
                if (billDebt.compareTo(BigDecimal.ZERO) > 0) {
                    totalHistoricalDebt = totalHistoricalDebt.add(billDebt);
                    log.info("历史账单 - 年度: {}, 应缴: {}, 已缴: {}, 欠费: {}",
                            historicalBill.getHeatYear(),
                            historicalBill.getTotalAmount(),
                            historicalBill.getPaidAmount(),
                            billDebt);
                }
            }

            log.info("历史欠费金额计算完成，总计: {} 元", totalHistoricalDebt);
            return totalHistoricalDebt;

        } catch (Exception e) {
            log.error("计算历史欠费金额失败，房屋ID: {}, 错误: {}", houseId, e.getMessage(), e);
            // 发生异常时返回0，避免影响主流程
            return BigDecimal.ZERO;
        }
    }

    /**
     * 获取简化的账单信息
     * 根据用热状态返回不同的费用信息，严格按照账单表数据
     * @param request 简化账单信息请求
     * @return 简化账单信息响应
     */
    @Override
    public SimpleBillInfoResponse getSimpleBillInfo(SimpleBillInfoRequest request) {
        try {
            log.info("开始获取简化账单信息: {}", request);

            // 1. 参数验证
            Long houseId = request.getHouseId();
            if (houseId == null) {
                return buildErrorResponse("房屋ID不能为空");
            }

            // 2. 获取房屋信息
            Optional<House> houseOpt = houseRepository.findById(houseId);
            if (!houseOpt.isPresent()) {
                return buildErrorResponse("房屋信息不存在");
            }
            House house = houseOpt.get();
            log.info("房屋信息: ID={}, 户号={}, 用热状态={}", house.getId(), house.getHouseNumber(), house.getIsHeating());

            // 3. 获取供暖年度（如果未指定则使用当前年度）
            Integer heatingYear = request.getHeatingYear();
            if (heatingYear == null) {
                heatingYear = getCurrentHeatingYear();
            }
            log.info("查询供暖年度: {}", heatingYear);

            // 4. 查询账单信息
            Optional<TBill> billOpt = billRepository.findByHouseIdAndHeatYear(houseId, heatingYear);

            // 5. 构建响应数据
            SimpleBillInfoResponse response = new SimpleBillInfoResponse();
            SimpleBillInfoResponse.BillData billData = new SimpleBillInfoResponse.BillData();

            // 6. 构建房屋信息（无论是否有账单都要显示）
            SimpleBillInfoResponse.HouseInfo houseInfo = buildHouseInfo(house, heatingYear);
            billData.setHouseInfo(houseInfo);

            if (!billOpt.isPresent()) {
                // 没有账单时，返回基本信息和空的账单信息
                log.info("未找到{}年度的账单信息，返回基本房屋信息", heatingYear);

                response.setCode(200);
                response.setMessage("未找到" + heatingYear + "年度的账单信息");

                // 设置空的账单信息
                billData.setBillId(null);
                billData.setBillFeeInfo(buildEmptyBillFeeInfo());
                billData.setPaymentStatusInfo(buildEmptyPaymentStatusInfo());
                billData.setPaymentRecords(new ArrayList<>());

                response.setData(billData);
                return response;
            }

            TBill bill = billOpt.get();
            log.info("账单信息: ID={}, 总金额={}, 已缴金额={}, 欠费金额={}, 状态={}",
                    bill.getId(), bill.getTotalAmount(), bill.getPaidAmount(), bill.getOverdueAmount(), bill.getStatus());

            // 有账单时，设置成功响应
            response.setCode(200);
            response.setMessage("获取账单信息成功");
            billData.setBillId(bill.getId());

            // 7. 构建账单费用信息（根据用热状态）
            SimpleBillInfoResponse.BillFeeInfo billFeeInfo = buildBillFeeInfo(house, bill);
            billData.setBillFeeInfo(billFeeInfo);

            // 8. 构建缴费状态信息
            SimpleBillInfoResponse.PaymentStatusInfo paymentStatusInfo = buildPaymentStatusInfo(bill);
            billData.setPaymentStatusInfo(paymentStatusInfo);

            // 9. 构建缴费记录信息
            List<SimpleBillInfoResponse.PaymentRecord> paymentRecords = buildSimplePaymentRecords(bill.getId());
            billData.setPaymentRecords(paymentRecords);

            response.setData(billData);

            log.info("简化账单信息获取成功");
            return response;

        } catch (Exception e) {
            log.error("获取简化账单信息失败", e);
            return buildErrorResponse("获取账单信息失败: " + e.getMessage());
        }
    }

    /**
     * 构建房屋信息
     * @param house 房屋实体
     * @param heatingYear 供暖年度
     * @return 房屋信息
     */
    private SimpleBillInfoResponse.HouseInfo buildHouseInfo(House house, Integer heatingYear) {
        log.info("构建房屋信息");

        SimpleBillInfoResponse.HouseInfo houseInfo = new SimpleBillInfoResponse.HouseInfo();
        houseInfo.setHouseId(house.getId());
        houseInfo.setHouseNumber(house.getHouseNumber());
        // 调用服务方法获取小区名字
        String communityName = heatUnitService.getCommunityNameByHouseId(house.getId());
        houseInfo.setAddress(communityName+" "+house.getRoomNo());
        houseInfo.setArea(house.getArea());
        houseInfo.setIsHeating(house.getIsHeating());
        houseInfo.setHeatingYear(heatingYear);

        // 设置用热状态文本
        String heatingStatusText = (house.getIsHeating() != null && house.getIsHeating() == 1) ? "正常供暖" : "不供暖";
        houseInfo.setHeatingStatusText(heatingStatusText);

        log.info("房屋信息构建完成: 用热状态={}", heatingStatusText);
        return houseInfo;
    }

    /**
     * 构建账单费用信息
     * 根据用热状态返回不同的费用信息
     * @param house 房屋实体
     * @param bill 账单实体
     * @return 账单费用信息
     */
    private SimpleBillInfoResponse.BillFeeInfo buildBillFeeInfo(House house, TBill bill) {
        log.info("构建账单费用信息，用热状态: {}", house.getIsHeating());

        SimpleBillInfoResponse.BillFeeInfo billFeeInfo = new SimpleBillInfoResponse.BillFeeInfo();

        // 1. 获取单价信息
        BigDecimal unitPrice = heatingFeeRuleService.getUnitPriceByRuleId(bill.getHeatFeeRuleId().longValue());
        billFeeInfo.setUnitPrice(unitPrice);
        log.info("单价: {} 元/㎡", unitPrice);

        // 2. 根据用热状态确定费用类型和金额
        boolean isHeating = house.getIsHeating() != null && house.getIsHeating() == 1;
        billFeeInfo.setAmount(bill.getTotalAmount());
        if (isHeating) {
            // 用热状态：显示用热费，金额为账单表的total_amount
            billFeeInfo.setHeatingFee(bill.getTotalAmount());
            billFeeInfo.setFeeTypeName("用热费");
            log.info("用热状态 - 用热费: {} 元", bill.getTotalAmount());
        } else {
            // 不用热状态：显示管网维护费，金额为最低缴费金额
            BigDecimal maintenanceFee = calculateMaintenanceFee(house, bill);
            billFeeInfo.setHeatingFee(maintenanceFee);
            billFeeInfo.setFeeTypeName("管网维护费");
            log.info("不用热状态 - 管网维护费: {} 元", maintenanceFee);
        }

        // 2. 欠费金额：直接从账单表的overdue_amount字段获取
        BigDecimal overdueAmount = bill.getOverdueAmount() != null ? bill.getOverdueAmount() : BigDecimal.ZERO;
        billFeeInfo.setOverdueAmount(overdueAmount);
        log.info("欠费金额: {} 元", overdueAmount);

        // 3. 应缴费金额：用热费/管网维护费 + 欠费金额
        BigDecimal totalPayableAmount = billFeeInfo.getHeatingFee().add(overdueAmount);
        billFeeInfo.setTotalPayableAmount(totalPayableAmount);
        log.info("应缴费金额: {} 元", totalPayableAmount);

        // 4. 实际缴费金额：从账单表的paid_amount字段获取
        BigDecimal actualPaidAmount = bill.getPaidAmount() != null ? bill.getPaidAmount() : BigDecimal.ZERO;
        billFeeInfo.setActualPaidAmount(actualPaidAmount);
        log.info("实际缴费金额: {} 元", actualPaidAmount);

        return billFeeInfo;
    }

    /**
     * 计算管网维护费
     * 不用热状态下，根据计费规则计算最低缴费金额作为管网维护费
     * @param house 房屋实体
     * @param bill 账单实体
     * @return 管网维护费金额
     */
    private BigDecimal calculateMaintenanceFee(House house, TBill bill) {
        log.info("计算管网维护费，房屋面积: {}, 计费规则ID: {}", house.getArea(), bill.getHeatFeeRuleId());

        try {
            // 1. 获取计费规则
            Optional<THeatingFeeRule> ruleOpt = heatingFeeRuleRepository.findById(bill.getHeatFeeRuleId().longValue());
            if (!ruleOpt.isPresent()) {
                log.warn("未找到计费规则，使用账单原金额的30%作为管网维护费");
                return bill.getTotalAmount().multiply(new BigDecimal("0.3"));
            }

            THeatingFeeRule rule = ruleOpt.get();
            log.info("计费规则: 最低缴费比例={}", rule.getMinPaymentRate());

            // 2. 计算管网维护费 = 账单总金额 * 最低缴费比例
            BigDecimal maintenanceFee = bill.getTotalAmount().multiply(rule.getMinPaymentRate());

            log.info("管网维护费计算完成: {} 元 (账单金额 {} * 最低比例 {})",
                    maintenanceFee, bill.getTotalAmount(), rule.getMinPaymentRate());

            return maintenanceFee;

        } catch (Exception e) {
            log.error("计算管网维护费失败: {}", e.getMessage(), e);
            // 异常情况下使用账单金额的30%
            BigDecimal fallbackFee = bill.getTotalAmount().multiply(new BigDecimal("0.3"));
            log.info("使用默认比例30%计算管网维护费: {} 元", fallbackFee);
            return fallbackFee;
        }
    }

    /**
     * 构建缴费状态信息
     * @param bill 账单实体
     * @return 缴费状态信息
     */
    private SimpleBillInfoResponse.PaymentStatusInfo buildPaymentStatusInfo(TBill bill) {
        log.info("构建缴费状态信息，账单状态: {}", bill.getStatus());

        SimpleBillInfoResponse.PaymentStatusInfo paymentStatusInfo = new SimpleBillInfoResponse.PaymentStatusInfo();

        // 1. 设置缴费状态
        String paymentStatus = bill.getStatus() != null ? bill.getStatus().name() : "unknown";
        paymentStatusInfo.setPaymentStatus(paymentStatus);

        // 2. 设置缴费状态文本
        String paymentStatusText = getBillStatusText(paymentStatus);
        paymentStatusInfo.setPaymentStatusText(paymentStatusText);

        // 3. 判断是否显示实际缴费金额
        // 已缴费或部分缴费状态显示实际缴费金额，未缴费状态不显示
        boolean showActualPaidAmount = !"unpaid".equals(paymentStatus);
        paymentStatusInfo.setShowActualPaidAmount(showActualPaidAmount);

        // 4. 计算剩余未缴金额
        BigDecimal paidAmount = bill.getPaidAmount() != null ? bill.getPaidAmount() : BigDecimal.ZERO;
        BigDecimal totalAmount = bill.getTotalAmount();
        BigDecimal overdueAmount = bill.getOverdueAmount() != null ? bill.getOverdueAmount() : BigDecimal.ZERO;

        // 剩余金额 = 总金额 + 欠费金额 - 已缴金额
        BigDecimal remainingAmount = totalAmount.add(overdueAmount).subtract(paidAmount);
        // 确保剩余金额不为负数
        if (remainingAmount.compareTo(BigDecimal.ZERO) < 0) {
            remainingAmount = BigDecimal.ZERO;
        }
        paymentStatusInfo.setRemainingAmount(remainingAmount);

        // 5. 设置日期信息
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        paymentStatusInfo.setDueDate(bill.getDueDate() != null ? bill.getDueDate().format(dateFormatter) : null);
        paymentStatusInfo.setLastPaidDate(bill.getLastPaidDate() != null ? bill.getLastPaidDate().format(dateFormatter) : null);

        log.info("缴费状态信息构建完成: 状态={}, 显示实际缴费={}, 剩余金额={}",
                paymentStatusText, showActualPaidAmount, remainingAmount);

        return paymentStatusInfo;
    }

    /**
     * 构建简化版缴费记录列表
     * @param billId 账单ID
     * @return 缴费记录列表
     */
    private List<SimpleBillInfoResponse.PaymentRecord> buildSimplePaymentRecords(Long billId) {
        log.info("构建缴费记录列表，账单ID: {}", billId);

        List<SimpleBillInfoResponse.PaymentRecord> paymentRecords = new ArrayList<>();

        try {
            // 查询缴费记录
            List<TPayment> payments = paymentRepository.findByBillIdOrderByPaymentDateDesc(billId);
            log.info("找到缴费记录数量: {}", payments.size());

            for (TPayment payment : payments) {
                SimpleBillInfoResponse.PaymentRecord record = new SimpleBillInfoResponse.PaymentRecord();
                record.setPaymentId(payment.getId());
                record.setAmount(payment.getAmount());
                record.setPaymentMethod(payment.getPaymentMethod() != null ? payment.getPaymentMethod().name() : "unknown");
                record.setPaymentMethodText(getPaymentMethodText(payment.getPaymentMethod()));

                // 格式化日期
                DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
                record.setPaymentDate(payment.getPaymentDate() != null ?
                    payment.getPaymentDate().format(dateFormatter) : null);

                record.setTransactionNo(payment.getTransactionNo());
                record.setRemark(payment.getRemark());

                paymentRecords.add(record);

                log.info("缴费记录: ID={}, 金额={}, 方式={}, 日期={}",
                        payment.getId(), payment.getAmount(),
                        record.getPaymentMethodText(), record.getPaymentDate());
            }

        } catch (Exception e) {
            log.error("构建缴费记录失败: {}", e.getMessage(), e);
            // 发生异常时返回空列表，不影响主流程
        }

        log.info("缴费记录构建完成，共 {} 条记录", paymentRecords.size());
        return paymentRecords;
    }

    /**
     * 构建空的账单费用信息
     * 用于没有账单数据时的显示
     * @return 空的账单费用信息
     */
    private SimpleBillInfoResponse.BillFeeInfo buildEmptyBillFeeInfo() {
        SimpleBillInfoResponse.BillFeeInfo billFeeInfo = new SimpleBillInfoResponse.BillFeeInfo();
        billFeeInfo.setHeatingFee(BigDecimal.ZERO);
        billFeeInfo.setFeeTypeName("用热费");
        billFeeInfo.setUnitPrice(BigDecimal.ZERO);
        billFeeInfo.setOverdueAmount(BigDecimal.ZERO);
        billFeeInfo.setTotalPayableAmount(BigDecimal.ZERO);
        billFeeInfo.setActualPaidAmount(BigDecimal.ZERO);
        return billFeeInfo;
    }

    /**
     * 构建空的缴费状态信息
     * 用于没有账单数据时的显示
     * @return 空的缴费状态信息
     */
    private SimpleBillInfoResponse.PaymentStatusInfo buildEmptyPaymentStatusInfo() {
        SimpleBillInfoResponse.PaymentStatusInfo paymentStatusInfo = new SimpleBillInfoResponse.PaymentStatusInfo();
        paymentStatusInfo.setPaymentStatus("no_bill");
        paymentStatusInfo.setPaymentStatusText("暂无账单");
        paymentStatusInfo.setShowActualPaidAmount(false);
        paymentStatusInfo.setRemainingAmount(BigDecimal.ZERO);
        paymentStatusInfo.setDueDate(null);
        paymentStatusInfo.setLastPaidDate(null);
        return paymentStatusInfo;
    }

    /**
     * 构建错误响应
     * @param errorMessage 错误消息
     * @return 错误响应
     */
    private SimpleBillInfoResponse buildErrorResponse(String errorMessage) {
        SimpleBillInfoResponse response = new SimpleBillInfoResponse();
        response.setCode(400);
        response.setMessage(errorMessage);
        response.setData(null);
        return response;
    }
}








